import pandas as pd
import json

# Read the CSV file
df = pd.read_csv('course_recommendation_data.csv')

# Convert to JavaScript format
js_data = []
for index, row in df.iterrows():
    course_obj = {
        'id': row['User_ID'],
        'age': row['Age'],
        'education': row['Education_Level'],
        'goal': row['Goal'],
        'category': row['Preferred_Category'],
        'subcategory': row['Subcategory'],
        'course': row['Course_Name'],
        'rating': row['Rating']
    }
    js_data.append(course_obj)

# Write to JavaScript file
with open('course_data.js', 'w') as f:
    f.write('const courseData = [\n')
    for i, course in enumerate(js_data):
        f.write(f'    {json.dumps(course)}')
        if i < len(js_data) - 1:
            f.write(',\n')
        else:
            f.write('\n')
    f.write('];\n')

print(f"Converted {len(js_data)} courses to JavaScript format!")
print("File saved as 'course_data.js'")

# Also create a summary
categories = df['Preferred_Category'].unique()
subcategories = df['Subcategory'].unique()

print(f"\nCategories ({len(categories)}):")
for cat in sorted(categories):
    print(f"  - {cat}")

print(f"\nSubcategories ({len(subcategories)}):")
for sub in sorted(subcategories):
    print(f"  - {sub}")
