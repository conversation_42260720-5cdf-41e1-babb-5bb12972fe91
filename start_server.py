#!/usr/bin/env python3
"""
Startup script for the Course Recommendation System
This script will install dependencies and start the Flask server
"""

import subprocess
import sys
import os

def install_requirements():
    """Install required packages"""
    print("Installing required packages...")
    try:
        subprocess.check_call([sys.executable, "-m", "pip", "install", "-r", "requirements.txt"])
        print("✅ Dependencies installed successfully!")
    except subprocess.CalledProcessError as e:
        print(f"❌ Error installing dependencies: {e}")
        return False
    return True

def start_server():
    """Start the Flask server"""
    print("Starting Course Recommendation Server...")
    print("🚀 Server will be available at: http://localhost:5000")
    print("🌐 Open index.html in your browser to use the frontend")
    print("⏹️  Press Ctrl+C to stop the server")
    print("-" * 50)
    
    try:
        subprocess.run([sys.executable, "app.py"])
    except KeyboardInterrupt:
        print("\n👋 Server stopped by user")
    except Exception as e:
        print(f"❌ Error starting server: {e}")

if __name__ == "__main__":
    print("🎓 Course Recommendation System")
    print("=" * 40)
    
    # Check if requirements.txt exists
    if not os.path.exists("requirements.txt"):
        print("❌ requirements.txt not found!")
        sys.exit(1)
    
    # Check if app.py exists
    if not os.path.exists("app.py"):
        print("❌ app.py not found!")
        sys.exit(1)
    
    # Install dependencies
    if install_requirements():
        # Start server
        start_server()
    else:
        print("❌ Failed to install dependencies. Please check the error messages above.")
        sys.exit(1)
