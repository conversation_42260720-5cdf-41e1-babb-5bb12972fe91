from flask import Flask, request, jsonify
from flask_cors import CORS
import pandas as pd
import numpy as np
from sklearn.preprocessing import OneHotEncoder
from sklearn.ensemble import RandomForestClassifier
import pickle
import os

app = Flask(__name__)
CORS(app)  # Enable CORS for frontend requests

# Global variables for model and encoder
model = None
encoder = None
df = None

def load_and_train_model():
    """Load data and train the model"""
    global model, encoder, df
    
    # Load dataset
    df = pd.read_csv("course_recommendation_data.csv")
    
    # Features and target
    features = ['Age', 'Education_Level', 'Goal', 'Preferred_Category', 'Subcategory']
    target = 'Course_Name'
    
    # Encode features
    encoder = OneHotEncoder()
    X = encoder.fit_transform(df[features])
    y = df[target]
    
    # Initialize and train model
    model = RandomForestClassifier(n_estimators=100, random_state=42)
    model.fit(X, y)
    
    print("Model trained successfully!")
    print(f"Model accuracy on training data: {model.score(X, y):.2f}")

def recommend_courses_classification(user_input, top_n=10):
    """Get course recommendations using the trained model"""
    try:
        user_df = pd.DataFrame([user_input])
        user_encoded = encoder.transform(user_df)
        proba = model.predict_proba(user_encoded)[0]
        course_labels = model.classes_
        
        top_indices = np.argsort(proba)[::-1][:top_n]
        top_courses = []
        
        for i in top_indices:
            course_name = course_labels[i]
            confidence = round(proba[i] * 100, 2)
            
            # Get course details from original dataset
            course_info = df[df['Course_Name'] == course_name].iloc[0]
            
            course_data = {
                'id': int(course_info['User_ID']),
                'course': course_name,
                'category': course_info['Preferred_Category'],
                'subcategory': course_info['Subcategory'],
                'rating': float(course_info['Rating']),
                'confidence': confidence,
                'age': int(course_info['Age']),
                'education': course_info['Education_Level'],
                'goal': course_info['Goal']
            }
            top_courses.append(course_data)
        
        return top_courses
    except Exception as e:
        print(f"Error in recommendation: {e}")
        return []

@app.route('/api/recommend', methods=['POST'])
def get_recommendations():
    """API endpoint to get course recommendations"""
    try:
        data = request.json
        
        # Validate required fields
        required_fields = ['age', 'education', 'goal', 'category', 'subcategory']
        for field in required_fields:
            if field not in data or not data[field]:
                return jsonify({'error': f'Missing required field: {field}'}), 400
        
        # Prepare user input for model
        user_input = {
            'Age': int(data['age']),
            'Education_Level': data['education'],
            'Goal': data['goal'],
            'Preferred_Category': data['category'],
            'Subcategory': data['subcategory']
        }
        
        # Get recommendations from model
        recommendations = recommend_courses_classification(user_input, top_n=15)
        
        return jsonify({
            'success': True,
            'recommendations': recommendations,
            'total_courses': len(recommendations)
        })
        
    except Exception as e:
        return jsonify({'error': str(e)}), 500

@app.route('/api/categories', methods=['GET'])
def get_categories():
    """Get all available categories and subcategories"""
    try:
        categories = {}
        for category in df['Preferred_Category'].unique():
            subcategories = df[df['Preferred_Category'] == category]['Subcategory'].unique().tolist()
            categories[category] = subcategories
        
        return jsonify({
            'success': True,
            'categories': categories
        })
    except Exception as e:
        return jsonify({'error': str(e)}), 500

@app.route('/api/search', methods=['GET'])
def search_subcategories():
    """Search subcategories by keyword"""
    try:
        query = request.args.get('q', '').lower()
        if not query:
            return jsonify({'success': True, 'subcategories': []})
        
        all_subcategories = df['Subcategory'].unique()
        matching_subcategories = [
            sub for sub in all_subcategories 
            if query in sub.lower()
        ]
        
        return jsonify({
            'success': True,
            'subcategories': matching_subcategories
        })
    except Exception as e:
        return jsonify({'error': str(e)}), 500

@app.route('/api/health', methods=['GET'])
def health_check():
    """Health check endpoint"""
    return jsonify({
        'success': True,
        'message': 'Course Recommendation API is running',
        'model_loaded': model is not None
    })

if __name__ == '__main__':
    print("Loading and training model...")
    load_and_train_model()
    print("Starting Flask server...")
    app.run(debug=True, host='0.0.0.0', port=5000)
