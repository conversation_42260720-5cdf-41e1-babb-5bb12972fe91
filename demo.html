<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Course Recommendation Demo</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
            background: #f5f5f5;
        }
        .demo-section {
            background: white;
            padding: 20px;
            margin: 20px 0;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        .feature {
            margin: 15px 0;
            padding: 10px;
            background: #e8f4fd;
            border-left: 4px solid #2196F3;
        }
        .btn {
            background: #2196F3;
            color: white;
            padding: 10px 20px;
            border: none;
            border-radius: 5px;
            cursor: pointer;
            text-decoration: none;
            display: inline-block;
            margin: 10px 5px;
        }
        .btn:hover {
            background: #1976D2;
        }
        .search-demo {
            background: #fff3cd;
            padding: 15px;
            border-radius: 5px;
            margin: 10px 0;
        }
    </style>
</head>
<body>
    <h1>🎓 Course Recommendation System Demo</h1>
    
    <div class="demo-section">
        <h2>Features Overview</h2>
        <div class="feature">
            <strong>📊 Age Slider:</strong> Interactive slider to select your age (18-50)
        </div>
        <div class="feature">
            <strong>🎓 Education Level:</strong> Dropdown for High School, Bachelors, Masters, PhD
        </div>
        <div class="feature">
            <strong>🎯 Goal Selection:</strong> Choose from Job, Upskilling, Switching Career, Freelancing
        </div>
        <div class="feature">
            <strong>📚 Category Selection:</strong> 10 main categories including Programming, Design, Business, etc.
        </div>
        <div class="feature">
            <strong>🔍 Smart Search:</strong> Search subcategories with auto-suggestions
        </div>
        <div class="feature">
            <strong>🎠 Course Slider:</strong> Interactive carousel showing recommended courses
        </div>
        <div class="feature">
            <strong>📈 Statistics:</strong> Real-time stats showing total courses, average rating, top-rated courses
        </div>
    </div>

    <div class="demo-section">
        <h2>Search Examples</h2>
        <div class="search-demo">
            <strong>Try searching for:</strong>
            <ul>
                <li><code>"machine"</code> → Shows Machine Learning courses</li>
                <li><code>"web"</code> → Shows Web Development courses</li>
                <li><code>"design"</code> → Shows Graphic Design, UI/UX courses</li>
                <li><code>"photo"</code> → Shows Photography and Photo Editing courses</li>
                <li><code>"data"</code> → Shows Data Science courses</li>
            </ul>
        </div>
    </div>

    <div class="demo-section">
        <h2>Sample Data</h2>
        <p>The system includes <strong>2000+ courses</strong> across:</p>
        <ul>
            <li><strong>10 Categories:</strong> Programming, Design, Business, Writing, Photography, Health & Fitness, Language Learning, Music, Personal Development, Engineering</li>
            <li><strong>36 Subcategories:</strong> Machine Learning, Web Development, UI/UX, Graphic Design, Marketing, and many more</li>
            <li><strong>Rating System:</strong> All courses have ratings from 3.0 to 5.0 stars</li>
        </ul>
    </div>

    <div class="demo-section">
        <h2>How It Works</h2>
        <ol>
            <li><strong>Set Your Preferences:</strong> Use the age slider and dropdown menus</li>
            <li><strong>Search Subcategories:</strong> Type keywords to find relevant subcategories</li>
            <li><strong>Get Recommendations:</strong> Click the button to see personalized course suggestions</li>
            <li><strong>Browse Results:</strong> Use the slider navigation to explore all recommended courses</li>
            <li><strong>View Statistics:</strong> See summary stats about your recommendations</li>
        </ol>
    </div>

    <div class="demo-section">
        <h2>Launch the App</h2>
        <p>Ready to find your perfect course? Click below to start:</p>
        <a href="index.html" class="btn">🚀 Launch Course Recommendation System</a>
    </div>

    <div class="demo-section">
        <h2>Technical Details</h2>
        <p><strong>Built with:</strong></p>
        <ul>
            <li>HTML5 & CSS3 with modern responsive design</li>
            <li>Vanilla JavaScript (no frameworks required)</li>
            <li>Font Awesome icons for beautiful UI</li>
            <li>CSV data converted to JavaScript format</li>
            <li>Smart filtering and search algorithms</li>
        </ul>
    </div>
</body>
</html>
