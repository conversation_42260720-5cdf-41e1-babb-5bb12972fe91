# 🎓 Course Recommendation System

A beautiful, interactive frontend for course recommendations built with HTML, CSS, and JavaScript. Features sliders, search functionality, and a responsive design.

## ✨ Features

### 🎛️ Interactive Controls
- **Age Slider**: Smooth range slider for age selection (18-50)
- **Education Level**: Dropdown for High School, Bachelors, Masters, PhD
- **Goal Selection**: Choose from Job, Upskilling, Switching Career, Freelancing
- **Category Selection**: 10 main categories including Programming, Design, Business, etc.

### 🔍 Smart Search
- **Subcategory Search**: Type keywords to find relevant subcategories
- **Auto-suggestions**: Real-time suggestions as you type
- **Example searches**: "machine" → Machine Learning, "web" → Web Development

### 🎠 Course Display
- **Interactive Slider**: Carousel-style course display with navigation
- **Course Cards**: Beautiful cards showing course details, ratings, and categories
- **Star Ratings**: Visual star ratings for each course
- **Responsive Design**: Works on desktop and mobile devices

### 📊 Statistics
- **Total Courses**: Shows number of recommended courses
- **Average Rating**: Calculates average rating of recommendations
- **Top Rated**: Counts courses with 4.5+ star ratings

### 🤖 Machine Learning Integration
- **Real ML Model**: Uses your trained RandomForest classifier
- **Confidence Scores**: Shows model confidence for each recommendation
- **API-Based**: Frontend calls Flask API that serves the ML model
- **Trained Model**: Uses the exact model from your MODEL.PY file

## 🚀 Quick Start

### Method 1: Easy Startup (Recommended)
1. **Run the startup script**: `python3 start_server.py`
2. **Open the frontend**: Open `index.html` in your browser
3. **Use the system**: Set preferences and get ML-powered recommendations!

### Method 2: Manual Setup
1. **Install dependencies**: `pip install -r requirements.txt`
2. **Start the server**: `python3 app.py`
3. **Open the frontend**: Open `index.html` in your browser
4. **Set preferences**: Use the sliders and dropdowns to set your preferences
5. **Search**: Type in the subcategory search to find specific topics
6. **Get recommendations**: Click "Get Recommendations" button (uses ML model!)
7. **Browse results**: Scroll through the recommended courses with confidence scores

## 📁 File Structure

```
├── index.html          # Main frontend application
├── styles.css          # CSS styling
├── script.js           # JavaScript functionality (calls ML model API)
├── course_data.js      # Course data (2000+ courses)
├── app.py              # Flask API server with ML model
├── MODEL.PY            # Original ML model code
├── start_server.py     # Easy startup script
├── requirements.txt    # Python dependencies
├── csv_to_js.py        # Python script to convert CSV to JS
├── course_recommendation_data.csv  # Original training data
└── README.md           # This file
```

## 🎯 How to Use

### Basic Usage
1. **Set Your Age**: Use the slider to select your age
2. **Choose Education**: Select your education level from dropdown
3. **Pick Your Goal**: Choose what you want to achieve
4. **Select Category**: Pick your preferred learning category
5. **Search Subcategory**: Type keywords to find specific topics
6. **Get Recommendations**: Click the button to see personalized suggestions

### Search Examples
- Type `"machine"` to find Machine Learning courses
- Type `"web"` to find Web Development courses  
- Type `"design"` to find Graphic Design and UI/UX courses
- Type `"photo"` to find Photography courses
- Type `"data"` to find Data Science courses

### Navigation
- Use **left/right arrows** to navigate through course recommendations
- **Hover over course cards** for interactive effects
- **View statistics** at the bottom to see recommendation summary

## 🛠️ Technical Details

### Technologies Used
- **HTML5**: Semantic markup and structure
- **CSS3**: Modern styling with gradients, animations, and responsive design
- **JavaScript**: Vanilla JS for all functionality (no frameworks)
- **Font Awesome**: Icons for beautiful UI elements

### Data Processing
- **2000+ courses** from CSV file
- **10 categories**: Programming, Design, Business, Writing, Photography, Health & Fitness, Language Learning, Music, Personal Development, Engineering
- **36 subcategories**: Machine Learning, Web Development, UI/UX, etc.
- **Rating system**: 3.0 to 5.0 star ratings

### Algorithms
- **Smart filtering**: Matches user preferences with course data
- **Fuzzy search**: Finds courses even with partial matches
- **Rating-based sorting**: Shows highest-rated courses first
- **Fallback recommendations**: Shows related courses if no exact matches

## 🎨 Customization

### Adding More Courses
1. Update `course_recommendation_data.csv` with new course data
2. Run `python3 csv_to_js.py` to regenerate `course_data.js`
3. Refresh the application

### Styling Changes
- Edit `styles.css` to modify colors, fonts, and layout
- All CSS uses modern features like CSS Grid and Flexbox
- Responsive design with mobile-first approach

### Functionality Changes
- Edit `script.js` to modify search algorithms or add new features
- All code is well-commented and modular

## 📱 Responsive Design

The application works perfectly on:
- **Desktop**: Full-featured experience with large course cards
- **Tablet**: Optimized layout with touch-friendly controls
- **Mobile**: Compact design with stacked layout

## 🔧 Browser Support

- **Chrome**: Full support
- **Firefox**: Full support  
- **Safari**: Full support
- **Edge**: Full support

## 📈 Performance

- **Fast loading**: Minimal dependencies
- **Smooth animations**: CSS transitions and transforms
- **Efficient search**: Optimized filtering algorithms
- **Responsive**: Quick response to user interactions

## 🤝 Contributing

Feel free to:
- Add more course data
- Improve the search algorithms
- Enhance the UI/UX design
- Add new features like favorites or course comparison

## 📄 License

This project is open source and available under the MIT License.

---

**Enjoy finding your perfect course! 🎓✨**
