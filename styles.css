* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    min-height: 100vh;
    color: #333;
}

.container {
    max-width: 1200px;
    margin: 0 auto;
    padding: 20px;
}

header {
    text-align: center;
    margin-bottom: 40px;
    color: white;
}

header h1 {
    font-size: 2.5rem;
    margin-bottom: 10px;
    text-shadow: 2px 2px 4px rgba(0,0,0,0.3);
}

header p {
    font-size: 1.2rem;
    opacity: 0.9;
}

.main-content {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 30px;
    margin-bottom: 40px;
}

.input-section, .results-section {
    background: white;
    padding: 30px;
    border-radius: 15px;
    box-shadow: 0 10px 30px rgba(0,0,0,0.1);
}

.input-section h2, .results-section h2 {
    margin-bottom: 25px;
    color: #4a5568;
    font-size: 1.5rem;
}

.input-group {
    margin-bottom: 20px;
}

.input-group label {
    display: block;
    margin-bottom: 8px;
    font-weight: 600;
    color: #2d3748;
}

.slider {
    width: 100%;
    height: 8px;
    border-radius: 5px;
    background: #e2e8f0;
    outline: none;
    -webkit-appearance: none;
}

.slider::-webkit-slider-thumb {
    -webkit-appearance: none;
    appearance: none;
    width: 20px;
    height: 20px;
    border-radius: 50%;
    background: #667eea;
    cursor: pointer;
    box-shadow: 0 2px 6px rgba(0,0,0,0.2);
}

.slider::-moz-range-thumb {
    width: 20px;
    height: 20px;
    border-radius: 50%;
    background: #667eea;
    cursor: pointer;
    border: none;
    box-shadow: 0 2px 6px rgba(0,0,0,0.2);
}

.select-input, .search-input {
    width: 100%;
    padding: 12px 15px;
    border: 2px solid #e2e8f0;
    border-radius: 8px;
    font-size: 16px;
    transition: border-color 0.3s ease;
}

.select-input:focus, .search-input:focus {
    outline: none;
    border-color: #667eea;
    box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.1);
}

.suggestions {
    position: relative;
    background: white;
    border: 1px solid #e2e8f0;
    border-radius: 8px;
    max-height: 200px;
    overflow-y: auto;
    display: none;
    z-index: 1000;
}

.suggestion-item {
    padding: 10px 15px;
    cursor: pointer;
    border-bottom: 1px solid #f7fafc;
    transition: background-color 0.2s ease;
}

.suggestion-item:hover {
    background-color: #f7fafc;
}

.suggestion-item:last-child {
    border-bottom: none;
}

.btn-primary {
    width: 100%;
    padding: 15px;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    border: none;
    border-radius: 8px;
    font-size: 16px;
    font-weight: 600;
    cursor: pointer;
    transition: transform 0.2s ease, box-shadow 0.2s ease;
}

.btn-primary:hover {
    transform: translateY(-2px);
    box-shadow: 0 5px 15px rgba(102, 126, 234, 0.4);
}

.course-slider {
    margin-bottom: 30px;
}

.slider-container {
    position: relative;
    width: 100%;
}

.slider-btn {
    display: none;
}

.courses-wrapper {
    width: 100%;
    overflow: hidden;
    border-radius: 10px;
    height: 400px;
    border: 2px solid #e2e8f0;
}

.courses-container {
    display: flex;
    flex-direction: column;
    gap: 15px;
    padding: 20px;
    height: 100%;
    overflow-y: auto;
    transition: none;
}

.course-card {
    width: 100%;
    background: linear-gradient(135deg, #f7fafc 0%, #edf2f7 100%);
    border-radius: 12px;
    padding: 15px;
    box-shadow: 0 4px 12px rgba(0,0,0,0.1);
    transition: transform 0.3s ease, box-shadow 0.3s ease;
    border-left: 4px solid #667eea;
    flex-shrink: 0;
}

.course-card:hover {
    transform: translateY(-5px);
    box-shadow: 0 8px 25px rgba(0,0,0,0.15);
}

.course-title {
    font-size: 1.2rem;
    font-weight: 700;
    color: #2d3748;
    margin-bottom: 10px;
}

.course-category {
    color: #667eea;
    font-weight: 600;
    margin-bottom: 8px;
}

.course-subcategory {
    color: #718096;
    margin-bottom: 15px;
}

.course-rating {
    display: flex;
    align-items: center;
    gap: 8px;
    margin-bottom: 10px;
}

.stars {
    color: #f6ad55;
}

.rating-value {
    font-weight: 600;
    color: #2d3748;
}

.empty-state {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    height: 100%;
    color: #718096;
    text-align: center;
}

.empty-state i {
    font-size: 3rem;
    margin-bottom: 15px;
    color: #cbd5e0;
}

.empty-state h3 {
    margin-bottom: 10px;
    color: #4a5568;
}

.no-courses {
    text-align: center;
    color: #718096;
    padding: 40px;
    font-style: italic;
}

.course-stats {
    display: flex;
    justify-content: space-around;
    background: #f7fafc;
    padding: 20px;
    border-radius: 10px;
    margin-top: 20px;
}

.stat-item {
    text-align: center;
}

.stat-number {
    display: block;
    font-size: 2rem;
    font-weight: 700;
    color: #667eea;
}

.stat-label {
    color: #718096;
    font-size: 0.9rem;
}

.loading-overlay {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: rgba(0,0,0,0.7);
    display: none;
    align-items: center;
    justify-content: center;
    z-index: 9999;
}

.loading-spinner {
    text-align: center;
    color: white;
}

.loading-spinner i {
    font-size: 3rem;
    margin-bottom: 20px;
}

.loading-spinner p {
    font-size: 1.2rem;
}

@media (max-width: 768px) {
    .main-content {
        grid-template-columns: 1fr;
        gap: 20px;
    }
    
    .course-card {
        min-width: 250px;
    }
    
    .course-stats {
        flex-direction: column;
        gap: 15px;
    }
    
    header h1 {
        font-size: 2rem;
    }
}



.course-details {
    color: #718096;
    font-size: 0.9rem;
}
