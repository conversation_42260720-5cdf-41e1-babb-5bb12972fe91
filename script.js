// Course data is loaded from course_data.js
// courseData variable is available globally

// DOM Elements
const ageSlider = document.getElementById('age');
const ageValue = document.getElementById('age-value');
const educationSelect = document.getElementById('education');
const goalSelect = document.getElementById('goal');
const categorySelect = document.getElementById('category');
const subcategorySearch = document.getElementById('subcategory-search');
const subcategorySelect = document.getElementById('subcategory');
const suggestionsDiv = document.getElementById('subcategory-suggestions');
const getRecommendationsBtn = document.getElementById('get-recommendations');
const coursesContainer = document.getElementById('courses-container');
const prevBtn = document.getElementById('prev-btn');
const nextBtn = document.getElementById('next-btn');
const loadingOverlay = document.getElementById('loading-overlay');

// Statistics elements
const totalCoursesSpan = document.getElementById('total-courses');
const avgRatingSpan = document.getElementById('avg-rating');
const topRatedSpan = document.getElementById('top-rated');

// Slider state
let currentSlideIndex = 0;
let coursesPerView = 1;
let filteredCourses = [];

// Get unique subcategories
const allSubcategories = [...new Set(courseData.map(course => course.subcategory))];

// Age slider functionality
ageSlider.addEventListener('input', function() {
    ageValue.textContent = this.value;
});

// Category change updates subcategories
categorySelect.addEventListener('change', function() {
    const selectedCategory = this.value;
    updateSubcategoryOptions(selectedCategory);
});

// Subcategory search functionality
subcategorySearch.addEventListener('input', function() {
    const searchTerm = this.value.toLowerCase();
    if (searchTerm.length > 0) {
        const filteredSubcategories = allSubcategories.filter(sub => 
            sub.toLowerCase().includes(searchTerm)
        );
        showSuggestions(filteredSubcategories);
    } else {
        hideSuggestions();
    }
});

// Hide suggestions when clicking outside
document.addEventListener('click', function(e) {
    if (!subcategorySearch.contains(e.target) && !suggestionsDiv.contains(e.target)) {
        hideSuggestions();
    }
});

// Get recommendations button
getRecommendationsBtn.addEventListener('click', getRecommendations);

// Slider navigation
prevBtn.addEventListener('click', () => navigateSlider(-1));
nextBtn.addEventListener('click', () => navigateSlider(1));

// Functions
function updateSubcategoryOptions(selectedCategory) {
    const categorySubcategories = courseData
        .filter(course => course.category === selectedCategory)
        .map(course => course.subcategory);
    
    const uniqueSubcategories = [...new Set(categorySubcategories)];
    
    subcategorySelect.innerHTML = '<option value="">Select Subcategory</option>';
    uniqueSubcategories.forEach(sub => {
        const option = document.createElement('option');
        option.value = sub;
        option.textContent = sub;
        subcategorySelect.appendChild(option);
    });
}

function showSuggestions(suggestions) {
    suggestionsDiv.innerHTML = '';
    if (suggestions.length > 0) {
        suggestions.forEach(suggestion => {
            const div = document.createElement('div');
            div.className = 'suggestion-item';
            div.textContent = suggestion;
            div.addEventListener('click', () => selectSuggestion(suggestion));
            suggestionsDiv.appendChild(div);
        });
        suggestionsDiv.style.display = 'block';
    } else {
        hideSuggestions();
    }
}

function selectSuggestion(suggestion) {
    subcategorySearch.value = suggestion;
    subcategorySelect.value = suggestion;
    hideSuggestions();
}

function hideSuggestions() {
    suggestionsDiv.style.display = 'none';
}

function getRecommendations() {
    const userInput = {
        age: parseInt(ageSlider.value),
        education: educationSelect.value,
        goal: goalSelect.value,
        category: categorySelect.value,
        subcategory: subcategorySelect.value || subcategorySearch.value
    };

    // Validate input
    if (!userInput.education || !userInput.goal || !userInput.category) {
        alert('Please fill in all required fields');
        return;
    }

    showLoading();

    // Simulate API call delay
    setTimeout(() => {
        filteredCourses = filterCourses(userInput);
        displayCourses(filteredCourses);
        updateStatistics(filteredCourses);
        hideLoading();
    }, 1500);
}

function filterCourses(userInput) {
    let filtered = courseData.filter(course => {
        let matches = true;
        
        // Age range matching (±5 years)
        if (Math.abs(course.age - userInput.age) > 5) {
            matches = false;
        }
        
        // Exact matches for other fields
        if (userInput.education && course.education !== userInput.education) {
            matches = false;
        }
        
        if (userInput.goal && course.goal !== userInput.goal) {
            matches = false;
        }
        
        if (userInput.category && course.category !== userInput.category) {
            matches = false;
        }
        
        if (userInput.subcategory && course.subcategory !== userInput.subcategory) {
            matches = false;
        }
        
        return matches;
    });

    // If no exact matches, broaden the search
    if (filtered.length === 0) {
        filtered = courseData.filter(course => {
            return course.category === userInput.category ||
                   course.goal === userInput.goal ||
                   (userInput.subcategory && course.subcategory.toLowerCase().includes(userInput.subcategory.toLowerCase()));
        });
    }

    // Sort by rating (highest first)
    return filtered.sort((a, b) => b.rating - a.rating);
}

function displayCourses(courses) {
    coursesContainer.innerHTML = '';
    currentSlideIndex = 0;

    if (courses.length === 0) {
        coursesContainer.innerHTML = '<div class="no-courses">No courses found. Try adjusting your preferences.</div>';
        return;
    }

    courses.forEach(course => {
        const courseCard = createCourseCard(course);
        coursesContainer.appendChild(courseCard);
    });

    updateSliderButtons();
}

function createCourseCard(course) {
    const card = document.createElement('div');
    card.className = 'course-card';
    
    const stars = generateStars(course.rating);
    
    card.innerHTML = `
        <div class="course-title">${course.course}</div>
        <div class="course-category">${course.category}</div>
        <div class="course-subcategory">${course.subcategory}</div>
        <div class="course-rating">
            <span class="stars">${stars}</span>
            <span class="rating-value">${course.rating}</span>
        </div>
        <div class="course-details">
            <small>Recommended for ${course.goal} • ${course.education}</small>
        </div>
    `;
    
    return card;
}

function generateStars(rating) {
    const fullStars = Math.floor(rating);
    const hasHalfStar = rating % 1 >= 0.5;
    let stars = '';
    
    for (let i = 0; i < fullStars; i++) {
        stars += '<i class="fas fa-star"></i>';
    }
    
    if (hasHalfStar) {
        stars += '<i class="fas fa-star-half-alt"></i>';
    }
    
    const emptyStars = 5 - fullStars - (hasHalfStar ? 1 : 0);
    for (let i = 0; i < emptyStars; i++) {
        stars += '<i class="far fa-star"></i>';
    }
    
    return stars;
}

function navigateSlider(direction) {
    const maxIndex = Math.max(0, filteredCourses.length - coursesPerView);
    currentSlideIndex = Math.max(0, Math.min(maxIndex, currentSlideIndex + direction));
    
    const translateX = -currentSlideIndex * (320); // 300px width + 20px gap
    coursesContainer.style.transform = `translateX(${translateX}px)`;
    
    updateSliderButtons();
}

function updateSliderButtons() {
    prevBtn.disabled = currentSlideIndex === 0;
    nextBtn.disabled = currentSlideIndex >= filteredCourses.length - coursesPerView;
}

function updateStatistics(courses) {
    const totalCourses = courses.length;
    const avgRating = courses.length > 0 ? (courses.reduce((sum, course) => sum + course.rating, 0) / courses.length).toFixed(1) : 0;
    const topRated = courses.filter(course => course.rating >= 4.5).length;

    totalCoursesSpan.textContent = totalCourses;
    avgRatingSpan.textContent = avgRating;
    topRatedSpan.textContent = topRated;
}

function showLoading() {
    loadingOverlay.style.display = 'flex';
}

function hideLoading() {
    loadingOverlay.style.display = 'none';
}

// Initialize with some sample data
window.addEventListener('load', function() {
    // Set default values
    ageSlider.value = 25;
    ageValue.textContent = '25';

    // Show some initial courses
    const initialCourses = courseData.slice(0, 10);
    displayCourses(initialCourses);
    updateStatistics(initialCourses);
    filteredCourses = initialCourses;
});
