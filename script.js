// Course data is loaded from course_data.js
// courseData variable is available globally

// DOM Elements
const ageSlider = document.getElementById('age');
const ageValue = document.getElementById('age-value');
const educationSelect = document.getElementById('education');
const goalSelect = document.getElementById('goal');
const categorySelect = document.getElementById('category');
const subcategorySearch = document.getElementById('subcategory-search');
const subcategorySelect = document.getElementById('subcategory');
const suggestionsDiv = document.getElementById('subcategory-suggestions');
const getRecommendationsBtn = document.getElementById('get-recommendations');
const coursesContainer = document.getElementById('courses-container');
const prevBtn = document.getElementById('prev-btn');
const nextBtn = document.getElementById('next-btn');
const loadingOverlay = document.getElementById('loading-overlay');

// Statistics elements
const totalCoursesSpan = document.getElementById('total-courses');
const avgRatingSpan = document.getElementById('avg-rating');
const topRatedSpan = document.getElementById('top-rated');

// Slider state
let currentSlideIndex = 0;
let coursesPerView = 1;
let filteredCourses = [];

// Get unique subcategories
const allSubcategories = [...new Set(courseData.map(course => course.subcategory))];

// Age slider functionality
ageSlider.addEventListener('input', function() {
    ageValue.textContent = this.value;
});

// Category change updates subcategories
categorySelect.addEventListener('change', function() {
    const selectedCategory = this.value;
    updateSubcategoryOptions(selectedCategory);
});

// Subcategory search functionality
subcategorySearch.addEventListener('input', function() {
    const searchTerm = this.value.toLowerCase();
    if (searchTerm.length > 0) {
        const filteredSubcategories = allSubcategories.filter(sub => 
            sub.toLowerCase().includes(searchTerm)
        );
        showSuggestions(filteredSubcategories);
    } else {
        hideSuggestions();
    }
});

// Hide suggestions when clicking outside
document.addEventListener('click', function(e) {
    if (!subcategorySearch.contains(e.target) && !suggestionsDiv.contains(e.target)) {
        hideSuggestions();
    }
});

// Get recommendations button
getRecommendationsBtn.addEventListener('click', getRecommendations);

// Functions
function updateSubcategoryOptions(selectedCategory) {
    const categorySubcategories = courseData
        .filter(course => course.category === selectedCategory)
        .map(course => course.subcategory);
    
    const uniqueSubcategories = [...new Set(categorySubcategories)];
    
    subcategorySelect.innerHTML = '<option value="">Select Subcategory</option>';
    uniqueSubcategories.forEach(sub => {
        const option = document.createElement('option');
        option.value = sub;
        option.textContent = sub;
        subcategorySelect.appendChild(option);
    });
}

function showSuggestions(suggestions) {
    suggestionsDiv.innerHTML = '';
    if (suggestions.length > 0) {
        suggestions.forEach(suggestion => {
            const div = document.createElement('div');
            div.className = 'suggestion-item';
            div.textContent = suggestion;
            div.addEventListener('click', () => selectSuggestion(suggestion));
            suggestionsDiv.appendChild(div);
        });
        suggestionsDiv.style.display = 'block';
    } else {
        hideSuggestions();
    }
}

function selectSuggestion(suggestion) {
    subcategorySearch.value = suggestion;
    subcategorySelect.value = suggestion;
    hideSuggestions();
}

function hideSuggestions() {
    suggestionsDiv.style.display = 'none';
}

async function getRecommendations() {
    const userInput = {
        age: parseInt(ageSlider.value),
        education: educationSelect.value,
        goal: goalSelect.value,
        category: categorySelect.value,
        subcategory: subcategorySelect.value || subcategorySearch.value
    };

    // Validate input
    if (!userInput.education || !userInput.goal || !userInput.category || !userInput.subcategory) {
        alert('Please fill in all required fields including subcategory');
        return;
    }

    showLoading();

    try {
        // Call the Python ML model script
        const command = `python3 generate_recommendations.py ${userInput.age} "${userInput.education}" "${userInput.goal}" "${userInput.category}" "${userInput.subcategory}"`;

        // Since we can't execute Python directly from browser, we'll show a message
        // In a real deployment, you would set up a proper backend

        // For now, let's simulate the ML model with intelligent filtering
        const recommendations = await simulateMLRecommendations(userInput);

        filteredCourses = recommendations;
        displayCourses(filteredCourses);
        updateStatistics(filteredCourses);

        // Show instructions for running the actual ML model
        console.log('To use the actual ML model, run this command in terminal:');
        console.log(command);

    } catch (error) {
        console.error('Error getting recommendations:', error);
        alert('Error getting recommendations.');
        showEmptyState();
        updateStatistics([]);
    } finally {
        hideLoading();
    }
}

// Simulate ML model recommendations with intelligent filtering
async function simulateMLRecommendations(userInput) {
    // This simulates what the ML model would return
    // Filter courses based on user preferences
    let filtered = courseData.filter(course => {
        let score = 0;

        // Exact matches get higher scores
        if (course.education === userInput.education) score += 30;
        if (course.goal === userInput.goal) score += 25;
        if (course.category === userInput.category) score += 20;
        if (course.subcategory === userInput.subcategory) score += 15;

        // Age proximity (closer ages get higher scores)
        const ageDiff = Math.abs(course.age - userInput.age);
        if (ageDiff <= 2) score += 10;
        else if (ageDiff <= 5) score += 5;

        // Rating bonus
        score += course.rating * 2;

        return score > 20; // Minimum threshold
    });

    // If no good matches, broaden the search
    if (filtered.length < 5) {
        filtered = courseData.filter(course =>
            course.category === userInput.category ||
            course.subcategory === userInput.subcategory ||
            course.goal === userInput.goal
        );
    }

    // Sort by rating and add confidence scores
    filtered = filtered
        .sort((a, b) => b.rating - a.rating)
        .slice(0, 15)
        .map((course, index) => ({
            ...course,
            confidence: Math.max(95 - index * 5, 60) // Simulate ML confidence
        }));

    return filtered;
}

// filterCourses function removed - now using ML model API

function displayCourses(courses) {
    coursesContainer.innerHTML = '';

    if (courses.length === 0) {
        coursesContainer.innerHTML = '<div class="no-courses">No courses found. Try adjusting your preferences.</div>';
        return;
    }

    courses.forEach(course => {
        const courseCard = createCourseCard(course);
        coursesContainer.appendChild(courseCard);
    });
}

function showEmptyState() {
    coursesContainer.innerHTML = `
        <div class="empty-state">
            <i class="fas fa-search"></i>
            <h3>Find Your Perfect Course</h3>
            <p>Set your preferences and click "Get Recommendations" to discover courses tailored for you!</p>
        </div>
    `;
}

function createCourseCard(course) {
    const card = document.createElement('div');
    card.className = 'course-card';

    const stars = generateStars(course.rating);

    card.innerHTML = `
        <div class="course-title">${course.course}</div>
        <div class="course-category">${course.category}</div>
        <div class="course-subcategory">${course.subcategory}</div>
        <div class="course-rating">
            <span class="stars">${stars}</span>
            <span class="rating-value">${course.rating}</span>
        </div>
        <div class="course-confidence">
            <small><strong>Model Confidence:</strong> ${course.confidence}%</small>
        </div>
        <div class="course-details">
            <small>Recommended for ${course.goal} • ${course.education}</small>
        </div>
    `;

    return card;
}

function generateStars(rating) {
    const fullStars = Math.floor(rating);
    const hasHalfStar = rating % 1 >= 0.5;
    let stars = '';
    
    for (let i = 0; i < fullStars; i++) {
        stars += '<i class="fas fa-star"></i>';
    }
    
    if (hasHalfStar) {
        stars += '<i class="fas fa-star-half-alt"></i>';
    }
    
    const emptyStars = 5 - fullStars - (hasHalfStar ? 1 : 0);
    for (let i = 0; i < emptyStars; i++) {
        stars += '<i class="far fa-star"></i>';
    }
    
    return stars;
}

// Slider navigation removed - using vertical scrolling instead

function updateStatistics(courses) {
    const totalCourses = courses.length;
    const avgRating = courses.length > 0 ? (courses.reduce((sum, course) => sum + course.rating, 0) / courses.length).toFixed(1) : 0;
    const topRated = courses.filter(course => course.rating >= 4.5).length;

    totalCoursesSpan.textContent = totalCourses;
    avgRatingSpan.textContent = avgRating;
    topRatedSpan.textContent = topRated;
}

function showLoading() {
    loadingOverlay.style.display = 'flex';
}

function hideLoading() {
    loadingOverlay.style.display = 'none';
}

// Initialize with empty state
window.addEventListener('load', function() {
    // Set default values
    ageSlider.value = 25;
    ageValue.textContent = '25';

    // Show empty state initially
    showEmptyState();
    updateStatistics([]);
    filteredCourses = [];
});
